# Kazakh-Lingo - Схема базы данных

## Обзор

Проект использует PostgreSQL как основную базу данных для хранения структурированных данных. База данных спроектирована для поддержки образовательной платформы изучения казахского языка.

## Таблицы базы данных

### 1. users - Пользователи системы

**Назначение**: Хранение информации о пользователях платформы

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    surname VARCHAR(500) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash BYTEA NOT NULL,
    image_url TEXT DEFAULT '',
    role VARCHAR(20) DEFAULT 'user' NOT NULL,
    activated BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_user_role CHECK (role IN ('admin', 'user'))
);

-- Индексы
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_role ON users(role);
```

**Поля**:
- `id` - уникальный идентификатор пользователя
- `name` - имя пользователя (до 500 символов)
- `surname` - фамилия пользователя (до 500 символов)
- `email` - уникальный email адрес
- `password_hash` - хэш пароля (bcrypt)
- `image_url` - URL изображения профиля
- `role` - роль пользователя ('admin' или 'user')
- `activated` - статус активации аккаунта
- `created_at` - дата создания аккаунта

### 2. auth_tokens - Токены авторизации

**Назначение**: Хранение JWT токенов для аутентификации

```sql
CREATE TABLE auth_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Индексы
CREATE INDEX idx_auth_tokens_user_id ON auth_tokens(user_id);
CREATE INDEX idx_auth_tokens_refresh_token ON auth_tokens(refresh_token);
CREATE INDEX idx_auth_tokens_expires_at ON auth_tokens(expires_at);
```

**Поля**:
- `id` - уникальный идентификатор токена
- `user_id` - ссылка на пользователя
- `access_token` - JWT access токен
- `refresh_token` - JWT refresh токен
- `created_at` - дата создания токена
- `expires_at` - дата истечения токена

### 3. words - Словарь

**Назначение**: Базовые единицы языка для обучения

```sql
CREATE TABLE words (
    id BIGSERIAL PRIMARY KEY,
    kaz_plaintext TEXT NOT NULL,
    rus_plaintext TEXT NOT NULL,
    audio_url TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы
CREATE INDEX idx_words_kaz_plaintext ON words(kaz_plaintext);
CREATE INDEX idx_words_rus_plaintext ON words(rus_plaintext);
```

**Поля**:
- `id` - уникальный идентификатор слова
- `kaz_plaintext` - слово на казахском языке
- `rus_plaintext` - перевод на русский язык
- `audio_url` - URL аудио произношения
- `created_at` - дата создания записи

### 4. sentences - Предложения

**Назначение**: Примеры предложений для изучения контекста

```sql
CREATE TABLE sentences (
    id BIGSERIAL PRIMARY KEY,
    kaz_plaintext TEXT NOT NULL,
    rus_plaintext TEXT NOT NULL,
    audio_url TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы
CREATE INDEX idx_sentences_kaz_plaintext ON sentences USING gin(to_tsvector('simple', kaz_plaintext));
CREATE INDEX idx_sentences_rus_plaintext ON sentences USING gin(to_tsvector('russian', rus_plaintext));
```

**Поля**:
- `id` - уникальный идентификатор предложения
- `kaz_plaintext` - предложение на казахском языке
- `rus_plaintext` - перевод на русский язык
- `audio_url` - URL аудио произношения
- `created_at` - дата создания записи

### 5. questions - Вопросы/Задания

**Назначение**: Интерактивные задания для проверки знаний

```sql
CREATE TABLE questions (
    id BIGSERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    correct_answer TEXT NOT NULL,
    image_url TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы
CREATE INDEX idx_questions_type ON questions(type);
CREATE INDEX idx_questions_created_at ON questions(created_at);
```

**Поля**:
- `id` - уникальный идентификатор вопроса
- `type` - тип вопроса (translation, audio, image, etc.)
- `correct_answer` - правильный ответ
- `image_url` - URL изображения для вопроса
- `created_at` - дата создания вопроса

### 6. question_words - Связь вопросов и слов

**Назначение**: Промежуточная таблица для связи many-to-many между вопросами и словами

```sql
CREATE TABLE question_words (
    question_id BIGINT NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    word_id BIGINT NOT NULL REFERENCES words(id) ON DELETE CASCADE,
    sequence_order INTEGER NOT NULL,
    PRIMARY KEY (question_id, word_id)
);

-- Индексы
CREATE INDEX idx_question_words_question_id ON question_words(question_id);
CREATE INDEX idx_question_words_word_id ON question_words(word_id);
CREATE INDEX idx_question_words_sequence ON question_words(question_id, sequence_order);
```

**Поля**:
- `question_id` - ссылка на вопрос
- `word_id` - ссылка на слово
- `sequence_order` - порядок слова в вопросе

### 7. theories - Теоретический материал

**Назначение**: Обучающий контент с теорией языка

```sql
CREATE TABLE theories (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    module_id BIGINT,
    tags TEXT[] DEFAULT '{}',
    examples_ids BIGINT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы
CREATE INDEX idx_theories_module_id ON theories(module_id);
CREATE INDEX idx_theories_title ON theories(title);
CREATE INDEX idx_theories_tags ON theories USING gin(tags);
```

**Поля**:
- `id` - уникальный идентификатор теории
- `title` - заголовок теории
- `description` - описание/содержание теории
- `module_id` - ссылка на модуль
- `tags` - массив тегов для категоризации
- `examples_ids` - массив ID примеров (предложений)
- `created_at` - дата создания теории

### 8. modules - Учебные модули

**Назначение**: Группировка теорий и вопросов в учебные блоки

```sql
CREATE TABLE modules (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    theory_ids BIGINT[] DEFAULT '{}',
    question_ids BIGINT[] DEFAULT '{}',
    pre_requisite_ids BIGINT[] DEFAULT '{}',
    level INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы
CREATE INDEX idx_modules_name ON modules(name);
CREATE INDEX idx_modules_level ON modules(level);
CREATE INDEX idx_modules_created_at ON modules(created_at);
```

**Поля**:
- `id` - уникальный идентификатор модуля
- `name` - название модуля
- `theory_ids` - массив ID теорий в модуле
- `question_ids` - массив ID вопросов в модуле
- `pre_requisite_ids` - массив ID предварительных модулей
- `level` - уровень сложности модуля
- `created_at` - дата создания модуля

### 9. progress - Прогресс обучения

**Назначение**: Отслеживание прогресса пользователей по модулям

```sql
CREATE TABLE progress (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    module_id BIGINT NOT NULL REFERENCES modules(id) ON DELETE CASCADE,
    mistaken_question_ids INTEGER[] DEFAULT '{}',
    time VARCHAR(10) NOT NULL,
    try_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы
CREATE INDEX idx_progress_user_id ON progress(user_id);
CREATE INDEX idx_progress_module_id ON progress(module_id);
CREATE INDEX idx_progress_user_module ON progress(user_id, module_id);
CREATE INDEX idx_progress_created_at ON progress(created_at);
```

**Поля**:
- `id` - уникальный идентификатор записи прогресса
- `user_id` - ссылка на пользователя
- `module_id` - ссылка на модуль
- `mistaken_question_ids` - массив ID вопросов с ошибками
- `time` - время прохождения модуля (HH:MM:SS)
- `try_count` - номер попытки прохождения
- `created_at` - дата создания записи
- `updated_at` - дата последнего обновления

### 10. achievements - Достижения

**Назначение**: Система достижений для мотивации пользователей

```sql
CREATE TABLE achievements (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    target INTEGER NOT NULL,
    key VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индексы
CREATE INDEX idx_achievements_type ON achievements(type);
CREATE INDEX idx_achievements_key ON achievements(key);
```

**Поля**:
- `id` - уникальный идентификатор достижения
- `name` - название достижения
- `description` - описание достижения
- `type` - тип достижения (module_completion, streak, etc.)
- `target` - целевое значение для получения достижения
- `key` - уникальный ключ достижения
- `created_at` - дата создания достижения
- `updated_at` - дата последнего обновления

### 11. user_achievements - Достижения пользователей

**Назначение**: Связь пользователей с их достижениями

```sql
CREATE TABLE user_achievements (
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    achievement_id BIGINT NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
    progress INTEGER DEFAULT 0,
    achieved BOOLEAN DEFAULT FALSE,
    achieved_at TIMESTAMP WITH TIME ZONE,
    PRIMARY KEY (user_id, achievement_id)
);

-- Индексы
CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_achievement_id ON user_achievements(achievement_id);
CREATE INDEX idx_user_achievements_achieved ON user_achievements(achieved);
```

**Поля**:
- `user_id` - ссылка на пользователя
- `achievement_id` - ссылка на достижение
- `progress` - текущий прогресс к достижению
- `achieved` - получено ли достижение
- `achieved_at` - дата получения достижения

## Связи между таблицами

### Диаграмма связей (ERD)

```
users (1) ←→ (N) auth_tokens
users (1) ←→ (N) progress
users (1) ←→ (N) user_achievements

modules (1) ←→ (N) progress
modules (1) ←→ (N) theories

questions (N) ←→ (N) words (через question_words)
theories (N) ←→ (N) sentences (через examples_ids)

achievements (1) ←→ (N) user_achievements
```

### Детальное описание связей:

1. **users → auth_tokens** (1:N)
   - Один пользователь может иметь несколько активных токенов
   - Каскадное удаление при удалении пользователя

2. **users → progress** (1:N)
   - Один пользователь может иметь прогресс по множеству модулей
   - Каскадное удаление при удалении пользователя

3. **users → user_achievements** (1:N)
   - Один пользователь может иметь множество достижений
   - Каскадное удаление при удалении пользователя

4. **modules → progress** (1:N)
   - Один модуль может быть пройден множеством пользователей
   - Каскадное удаление при удалении модуля

5. **modules → theories** (1:N через theory_ids)
   - Один модуль может содержать множество теорий
   - Связь через массив theory_ids в таблице modules

6. **questions → words** (N:N через question_words)
   - Один вопрос может содержать множество слов
   - Одно слово может использоваться в множестве вопросов
   - Порядок слов важен (sequence_order)

7. **theories → sentences** (N:N через examples_ids)
   - Одна теория может содержать множество примеров
   - Одно предложение может использоваться в множестве теорий

8. **achievements → user_achievements** (1:N)
   - Одно достижение может быть получено множеством пользователей
   - Каскадное удаление при удалении достижения

## Индексы и производительность

### Основные индексы:

1. **Первичные ключи** - автоматически создаются для всех таблиц
2. **Внешние ключи** - индексы на все foreign key поля
3. **Уникальные ключи** - на email пользователей и ключи достижений
4. **Поисковые индексы** - GIN индексы для полнотекстового поиска
5. **Составные индексы** - для часто используемых комбинаций полей

### Рекомендации по оптимизации:

1. **Партиционирование таблицы progress** по дате для больших объемов данных
2. **Архивирование старых токенов** для очистки таблицы auth_tokens
3. **Кэширование** часто запрашиваемых модулей и теорий
4. **Денормализация** для критически важных запросов

## Миграции базы данных

Проект использует migrate tool для управления схемой базы данных:

### Структура миграций:
```
migrations/
├── 000001_users.up.sql          # Создание таблицы users
├── 000001_users.down.sql        # Откат создания users
├── 000002_tokens.up.sql         # Создание таблицы tokens
├── 000002_tokens.down.sql       # Откат создания tokens
├── 000003_auth_tokens.up.sql    # Создание auth_tokens
├── 000003_auth_tokens.down.sql  # Откат auth_tokens
├── 000004_create_table_questions.up.sql
├── 000004_create_table_questions.down.sql
├── 000005_create_table_sentences.up.sql
├── 000005_create_table_sentences.down.sql
├── 000006_create_table_theories.up.sql
├── 000006_create_table_theories.down.sql
├── 000007_create_table_modules.up.sql
├── 000007_create_table_modules.down.sql
├── 000008_create_table_progress.up.sql
├── 000008_create_table_progress.down.sql
├── 000009_create_table_achievements.up.sql
├── 000009_create_table_achievements.down.sql
├── 000010_alter_table_modules.up.sql
├── 000010_alter_table_modules.down.sql
├── 000011_add_role_to_users.up.sql    # Добавление поля role в users
└── 000011_add_role_to_users.down.sql  # Откат добавления role
```

### Команды миграций:
```bash
# Применить все миграции
migrate -path ./migrations -database "postgres://user:pass@localhost/db?sslmode=disable" up

# Откатить последнюю миграцию
migrate -path ./migrations -database "postgres://user:pass@localhost/db?sslmode=disable" down 1

# Проверить версию
migrate -path ./migrations -database "postgres://user:pass@localhost/db?sslmode=disable" version
```

## Типы данных и ограничения

### Используемые типы данных:

1. **BIGSERIAL** - для первичных ключей (автоинкремент)
2. **BIGINT** - для внешних ключей и больших чисел
3. **VARCHAR(N)** - для строк с ограничением длины
4. **TEXT** - для длинных текстов без ограничения
5. **BYTEA** - для бинарных данных (хэши паролей)
6. **BOOLEAN** - для логических значений
7. **INTEGER** - для целых чисел
8. **TIMESTAMP WITH TIME ZONE** - для дат с часовым поясом
9. **TEXT[]** - массивы текстовых значений
10. **BIGINT[]** - массивы целых чисел

### Ограничения (Constraints):

1. **NOT NULL** - обязательные поля
2. **UNIQUE** - уникальные значения (email, ключи достижений)
3. **FOREIGN KEY** - ссылочная целостность
4. **CHECK** - проверочные ограничения (можно добавить для валидации)
5. **DEFAULT** - значения по умолчанию

## Безопасность базы данных

### Меры безопасности:

1. **Хэширование паролей** - bcrypt с cost factor 12
2. **Prepared statements** - защита от SQL инъекций
3. **Ограничение прав доступа** - минимальные необходимые права
4. **Шифрование соединения** - SSL/TLS для продакшн
5. **Аудит доступа** - логирование критических операций

### Рекомендации:

1. **Регулярные бэкапы** - автоматическое резервное копирование
2. **Мониторинг производительности** - отслеживание медленных запросов
3. **Обновления безопасности** - регулярное обновление PostgreSQL
4. **Ротация паролей** - периодическая смена паролей доступа

## Примеры запросов

### Получение полного модуля с контентом:
```sql
SELECT
    m.id, m.name, m.level, m.created_at,
    t.id as theory_id, t.title, t.description,
    q.id as question_id, q.type, q.correct_answer
FROM modules m
LEFT JOIN theories t ON t.id = ANY(m.theory_ids)
LEFT JOIN questions q ON q.id = ANY(m.question_ids)
WHERE m.id = $1;
```

### Получение прогресса пользователя:
```sql
SELECT
    u.name, u.surname,
    COUNT(p.id) as completed_modules,
    AVG(EXTRACT(EPOCH FROM p.time::interval)) as avg_time
FROM users u
LEFT JOIN progress p ON u.id = p.user_id
WHERE u.id = $1
GROUP BY u.id, u.name, u.surname;
```

### Расчет серии дней обучения:
```sql
WITH daily_progress AS (
    SELECT DISTINCT DATE(created_at) as study_date
    FROM progress
    WHERE user_id = $1
    ORDER BY study_date
)
SELECT
    COUNT(*) as total_days,
    MAX(study_date) as last_study_date
FROM daily_progress;
```
