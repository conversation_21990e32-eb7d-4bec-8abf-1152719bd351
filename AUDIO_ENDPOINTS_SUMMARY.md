# Новые эндпоинты для получения аудио файлов

## Обзор

Добавлены новые API эндпоинты для получения аудио файлов из MinIO storage, которые позволяют воспроизводить аудио файлы напрямую через API приложения.

## Новые эндпоинты

### 1. GET /v1/files/audio
**Получение аудио файла по полному MinIO URL**

```bash
GET /v1/files/audio?url=http://minio:9000/klingo-audio/334c81d6-e27d-4bc3-bdfe-d58580def252.mp3
```

**Параметры:**
- `url` (required) - полный URL файла из MinIO

**Особенности:**
- Проверяет, что URL содержит `klingo-audio` bucket
- Возвращает файл с правильным Content-Type
- Не требует аутентификации

### 2. GET /v1/files/get
**Получение файла по bucket и имени**

```bash
GET /v1/files/get?bucket=klingo-audio&filename=334c81d6-e27d-4bc3-bdfe-d58580def252.mp3
```

**Параметры:**
- `bucket` (required) - имя bucket (`klingo-audio` или `klingo-images`)
- `filename` (required) - имя файла

**Особенности:**
- Поддерживает как аудио, так и изображения
- Автоматически определяет Content-Type по расширению
- Не требует аутентификации

## Заголовки ответа

Оба эндпоинта возвращают следующие заголовки:
- `Content-Type`: определяется по расширению файла
- `Content-Length`: размер файла в байтах
- `Cache-Control`: public, max-age=3600 (кэш на 1 час)
- `Accept-Ranges`: bytes (поддержка частичной загрузки)

## Поддерживаемые форматы

### Аудио:
- `.mp3` → `audio/mpeg`
- `.wav` → `audio/wav`
- `.ogg` → `audio/ogg`
- `.m4a` → `audio/mp4`

### Изображения:
- `.jpg`, `.jpeg` → `image/jpeg`
- `.png` → `image/png`
- `.gif` → `image/gif`
- `.webp` → `image/webp`

## Использование в HTML

```html
<!-- Воспроизведение аудио -->
<audio controls>
  <source src="http://localhost:4000/v1/files/get?bucket=klingo-audio&filename=file.mp3" type="audio/mpeg">
  Ваш браузер не поддерживает аудио элемент.
</audio>

<!-- Отображение изображения -->
<img src="http://localhost:4000/v1/files/get?bucket=klingo-images&filename=image.jpg" alt="Изображение">
```

## Использование в JavaScript

```javascript
// Проверка доступности файла
const checkAudioFile = async (bucket, filename) => {
  const url = `http://localhost:4000/v1/files/get?bucket=${bucket}&filename=${filename}`;
  
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('Ошибка проверки файла:', error);
    return false;
  }
};

// Создание аудио элемента
const createAudioPlayer = (bucket, filename) => {
  const url = `http://localhost:4000/v1/files/get?bucket=${bucket}&filename=${filename}`;
  
  const audio = document.createElement('audio');
  audio.controls = true;
  audio.src = url;
  
  return audio;
};
```

## Коды ошибок

- **400 Bad Request** - Неверные параметры или недопустимый bucket
- **404 Not Found** - Файл не найден в MinIO
- **500 Internal Server Error** - Ошибка сервера или MinIO

## Тестирование

Используйте файл `test-audio-playback.html` для тестирования:

1. Откройте файл в браузере
2. Введите URL файла или bucket/filename
3. Нажмите кнопку для загрузки и воспроизведения
4. Проверьте, что аудио воспроизводится корректно

## Интеграция с существующими методами

Эти эндпоинты отлично работают с методами создания слов и предложений:

1. Создайте слово с аудио через `POST /v1/word/with-audio`
2. Получите `audio_url` из ответа
3. Используйте новые эндпоинты для воспроизведения:
   ```javascript
   // Если audio_url = "http://minio:9000/klingo-audio/file.mp3"
   const playbackUrl = "http://localhost:4000/v1/files/audio?url=" + encodeURIComponent(audio_url);
   
   // Или извлеките filename и используйте прямой доступ
   const filename = audio_url.split('/').pop();
   const directUrl = `http://localhost:4000/v1/files/get?bucket=klingo-audio&filename=${filename}`;
   ```

## Преимущества

1. **Единая точка доступа** - все файлы доступны через API приложения
2. **Правильные заголовки** - автоматическое определение Content-Type
3. **Кэширование** - файлы кэшируются на 1 час
4. **Безопасность** - контролируемый доступ к файлам
5. **Простота использования** - не нужно знать внутренние детали MinIO
6. **Поддержка Range** - возможность частичной загрузки для больших файлов
