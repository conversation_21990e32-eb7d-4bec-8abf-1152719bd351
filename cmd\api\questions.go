package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/olzzhas/kazakh-lingo/internal/data"
)

func (app *application) createQuestionHandler(w http.ResponseWriter, r *http.Request) {
	var input data.Question
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Вставляем вопрос
	err = app.models.Questions.Insert(&input)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"question": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) listQuestionsHandler(w http.ResponseWriter, r *http.Request) {
	questions, err := app.models.Questions.GetAll()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"question": questions}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) showQuestionHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	question, err := app.models.Questions.Get(id)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"question": question}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) createWordHandler(w http.ResponseWriter, r *http.Request) {
	var input data.Word
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	err = app.models.Words.Insert(&input)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"word": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// createWordWithAudioHandler создает слово с загрузкой аудио файла
func (app *application) createWordWithAudioHandler(w http.ResponseWriter, r *http.Request) {
	// Ограничиваем размер файла до 10MB
	err := r.ParseMultipartForm(10 << 20)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Получаем текстовые данные
	kazPlaintext := r.FormValue("kaz_plaintext")
	rusPlaintext := r.FormValue("rus_plaintext")

	if kazPlaintext == "" || rusPlaintext == "" {
		app.badRequestResponse(w, r, fmt.Errorf("kaz_plaintext and rus_plaintext are required"))
		return
	}

	// Создаем слово с пустым audio_url сначала
	word := &data.Word{
		KazPlaintext: kazPlaintext,
		RusPlaintext: rusPlaintext,
		AudioURL:     "", // Пока пустой
	}

	// Проверяем, есть ли аудио файл
	file, handler, err := r.FormFile("audio")
	if err == nil {
		// Файл есть, загружаем его
		defer file.Close()

		// Проверяем тип файла
		if !isValidAudioFile(handler.Filename) {
			app.badRequestResponse(w, r, fmt.Errorf("invalid audio file type. Allowed: mp3, wav, ogg, m4a"))
			return
		}

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Загружаем файл в MinIO
		fileURL, err := app.storages.MinIO.UploadAudio(ctx, file, handler)
		if err != nil {
			app.serverErrorResponse(w, r, err)
			return
		}

		word.AudioURL = fileURL
	}
	// Если файла нет, создаем слово без аудио

	// Вставляем слово в базу данных
	err = app.models.Words.Insert(word)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"word": word}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// isValidAudioFile проверяет, является ли файл допустимым аудио файлом
// func isValidAudioFile(filename string) bool {
// 	filename = strings.ToLower(filename)
// 	validExtensions := []string{".mp3", ".wav", ".ogg", ".m4a"}

// 	for _, ext := range validExtensions {
// 		if strings.HasSuffix(filename, ext) {
// 			return true
// 		}
// 	}
// 	return false
// }

func (app *application) listWordsHandler(w http.ResponseWriter, r *http.Request) {
	words, err := app.models.Words.GetAll()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"words": words}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) createSentenceHandler(w http.ResponseWriter, r *http.Request) {
	var input data.Sentence
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}
	err = app.models.Sentences.Insert(&input)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
	err = app.writeJSON(w, http.StatusOK, envelope{"sentence": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// createSentenceWithAudioHandler создает предложение с загрузкой аудио файла
func (app *application) createSentenceWithAudioHandler(w http.ResponseWriter, r *http.Request) {
	// Ограничиваем размер файла до 10MB
	err := r.ParseMultipartForm(10 << 20)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	// Получаем текстовые данные
	kazPlaintext := r.FormValue("kaz_plaintext")
	rusPlaintext := r.FormValue("rus_plaintext")

	if kazPlaintext == "" || rusPlaintext == "" {
		app.badRequestResponse(w, r, fmt.Errorf("kaz_plaintext and rus_plaintext are required"))
		return
	}

	// Создаем предложение с пустым audio_url сначала
	sentence := &data.Sentence{
		KazPlaintext: kazPlaintext,
		RusPlaintext: rusPlaintext,
		AudioURL:     "", // Пока пустой
	}

	// Проверяем, есть ли аудио файл
	file, handler, err := r.FormFile("audio")
	if err == nil {
		// Файл есть, загружаем его
		defer file.Close()

		// Проверяем тип файла
		if !isValidAudioFile(handler.Filename) {
			app.badRequestResponse(w, r, fmt.Errorf("invalid audio file type. Allowed: mp3, wav, ogg, m4a"))
			return
		}

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Загружаем файл в MinIO
		fileURL, err := app.storages.MinIO.UploadAudio(ctx, file, handler)
		if err != nil {
			app.serverErrorResponse(w, r, err)
			return
		}

		sentence.AudioURL = fileURL
	}
	// Если файла нет, создаем предложение без аудио

	// Вставляем предложение в базу данных
	err = app.models.Sentences.Insert(sentence)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"sentence": sentence}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) showSentenceHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	sentence, err := app.models.Sentences.Get(id)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"sentence": sentence}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

}

func (app *application) listSentencesHandler(w http.ResponseWriter, r *http.Request) {
	sentences, err := app.models.Sentences.GetAll()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"sentences": sentences}, nil)
}
