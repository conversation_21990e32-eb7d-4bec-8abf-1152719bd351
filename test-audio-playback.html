<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест воспроизведения аудио файлов</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        audio {
            width: 100%;
            margin: 10px 0;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .example-urls {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .example-urls code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Тест воспроизведения аудио файлов</h1>
    
    <div class="example-urls">
        <h3>Примеры URL для тестирования:</h3>
        <p><strong>Полный MinIO URL:</strong><br>
        <code>http://minio:9000/klingo-audio/334c81d6-e27d-4bc3-bdfe-d58580def252.mp3</code></p>
        
        <p><strong>Bucket:</strong> <code>klingo-audio</code><br>
        <strong>Filename:</strong> <code>334c81d6-e27d-4bc3-bdfe-d58580def252.mp3</code></p>
    </div>

    <div class="test-section">
        <h2>Метод 1: Получение аудио по полному URL</h2>
        <div class="form-group">
            <label for="fullUrl">Полный MinIO URL:</label>
            <input type="text" id="fullUrl" placeholder="http://minio:9000/klingo-audio/filename.mp3">
        </div>
        <button onclick="testAudioByUrl()">Загрузить и воспроизвести</button>
        <div id="audioByUrl"></div>
        <div id="resultByUrl" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Метод 2: Получение файла по bucket и имени</h2>
        <div class="form-group">
            <label for="bucket">Bucket:</label>
            <input type="text" id="bucket" placeholder="klingo-audio" value="klingo-audio">
        </div>
        <div class="form-group">
            <label for="filename">Имя файла:</label>
            <input type="text" id="filename" placeholder="334c81d6-e27d-4bc3-bdfe-d58580def252.mp3">
        </div>
        <button onclick="testAudioByBucketFilename()">Загрузить и воспроизвести</button>
        <div id="audioByBucketFilename"></div>
        <div id="resultByBucketFilename" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Метод 3: Прямое воспроизведение (без проверки)</h2>
        <div class="form-group">
            <label for="directUrl">URL для прямого воспроизведения:</label>
            <input type="text" id="directUrl" placeholder="http://localhost:4000/v1/files/audio?url=...">
        </div>
        <button onclick="playDirectAudio()">Воспроизвести напрямую</button>
        <div id="directAudio"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000/v1';

        function showResult(elementId, message, isError = false) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = message;
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.style.display = 'block';
        }

        function createAudioElement(src, containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <audio controls style="width: 100%; margin: 10px 0;">
                    <source src="${src}" type="audio/mpeg">
                    <source src="${src}" type="audio/wav">
                    <source src="${src}" type="audio/ogg">
                    Ваш браузер не поддерживает аудио элемент.
                </audio>
            `;
        }

        async function testAudioByUrl() {
            const fullUrl = document.getElementById('fullUrl').value.trim();
            if (!fullUrl) {
                showResult('resultByUrl', 'Введите полный URL файла', true);
                return;
            }

            try {
                const apiUrl = `${API_BASE}/files/audio?url=${encodeURIComponent(fullUrl)}`;
                
                // Проверяем доступность файла
                const response = await fetch(apiUrl, { method: 'HEAD' });
                
                if (response.ok) {
                    createAudioElement(apiUrl, 'audioByUrl');
                    showResult('resultByUrl', `Файл загружен успешно! Content-Type: ${response.headers.get('content-type')}`);
                } else {
                    showResult('resultByUrl', `Ошибка: ${response.status} ${response.statusText}`, true);
                }
            } catch (error) {
                showResult('resultByUrl', `Ошибка: ${error.message}`, true);
            }
        }

        async function testAudioByBucketFilename() {
            const bucket = document.getElementById('bucket').value.trim();
            const filename = document.getElementById('filename').value.trim();
            
            if (!bucket || !filename) {
                showResult('resultByBucketFilename', 'Введите bucket и имя файла', true);
                return;
            }

            try {
                const apiUrl = `${API_BASE}/files/get?bucket=${encodeURIComponent(bucket)}&filename=${encodeURIComponent(filename)}`;
                
                // Проверяем доступность файла
                const response = await fetch(apiUrl, { method: 'HEAD' });
                
                if (response.ok) {
                    createAudioElement(apiUrl, 'audioByBucketFilename');
                    showResult('resultByBucketFilename', `Файл загружен успешно! Content-Type: ${response.headers.get('content-type')}`);
                } else {
                    showResult('resultByBucketFilename', `Ошибка: ${response.status} ${response.statusText}`, true);
                }
            } catch (error) {
                showResult('resultByBucketFilename', `Ошибка: ${error.message}`, true);
            }
        }

        function playDirectAudio() {
            const directUrl = document.getElementById('directUrl').value.trim();
            if (!directUrl) {
                alert('Введите URL для воспроизведения');
                return;
            }

            createAudioElement(directUrl, 'directAudio');
        }

        // Заполняем примеры при загрузке страницы
        window.onload = function() {
            document.getElementById('fullUrl').value = 'http://minio:9000/klingo-audio/334c81d6-e27d-4bc3-bdfe-d58580def252.mp3';
            document.getElementById('filename').value = '334c81d6-e27d-4bc3-bdfe-d58580def252.mp3';
            document.getElementById('directUrl').value = 'http://localhost:4000/v1/files/get?bucket=klingo-audio&filename=334c81d6-e27d-4bc3-bdfe-d58580def252.mp3';
        };
    </script>
</body>
</html>
