# Руководство по админским эндпоинтам

## Обзор

Реализованы эндпоинты для управления пользователями в админ-панели. Все эндпоинты требуют аутентификации и прав администратора.

## Аутентификация

Все админские эндпоинты требуют:
1. **JWT токен** в заголовке `Authorization: Bearer <token>`
2. **Права администратора** (проверяется по полю `role = 'admin'` в профиле пользователя)

## Эндпоинты

### 1. GET /v1/admin/users
**Получение списка пользователей с фильтрацией**

**Параметры запроса:**
- `page` (optional) - номер страницы (по умолчанию: 1)
- `limit` (optional) - количество записей (по умолчанию: 20, максимум: 100)
- `search` (optional) - поиск по имени, фамилии или email
- `status` (optional) - фильтр по статусу ("active" или "blocked")
- `dateFrom` (optional) - фильтр по дате регистрации (от)
- `dateTo` (optional) - фильтр по дате регистрации (до)

**Пример:**
```bash
curl -X GET "http://localhost:4000/v1/admin/users?page=1&limit=20&search=айдар&status=active" \
  -H "Authorization: Bearer admin_token"
```

**Ответ:**
```json
{
  "data": {
    "users": [
      {
        "id": 1,
        "name": "Айдар",
        "surname": "Нурланов",
        "email": "<EMAIL>",
        "activated": true,
        "created_at": "2024-01-01T12:00:00Z",
        "last_activity": "2024-01-15T10:30:00Z",
        "completed_modules": 5,
        "total_time_spent": "02:45:30",
        "current_streak": 7
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 20,
    "total_pages": 1
  }
}
```

### 2. GET /v1/admin/users/{id}
**Получение детальной информации о пользователе**

**Пример:**
```bash
curl -X GET "http://localhost:4000/v1/admin/users/1" \
  -H "Authorization: Bearer admin_token"
```

**Ответ:**
```json
{
  "data": {
    "user": {
      "id": 1,
      "name": "Айдар",
      "surname": "Нурланов",
      "email": "<EMAIL>",
      "activated": true,
      "created_at": "2024-01-01T12:00:00Z"
    },
    "progress": {
      "completed_modules": 5,
      "total_modules": 20,
      "completion_percentage": 25.0,
      "total_time_spent": "02:45:30",
      "current_streak": 7
    },
    "achievements": [
      {
        "achievement_id": 1,
        "user_id": 1,
        "progress": 10,
        "achieved": true
      }
    ]
  }
}
```

### 3. PUT /v1/admin/users/{id}/status
**Обновление статуса пользователя (блокировка/разблокировка)**

**Тело запроса:**
```json
{
  "status": "blocked",
  "reason": "Нарушение правил использования"
}
```

**Пример:**
```bash
curl -X PUT "http://localhost:4000/v1/admin/users/1/status" \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{"status": "blocked", "reason": "Нарушение правил использования"}'
```

**Ответ:**
```json
{
  "message": "User status updated successfully",
  "user_id": 1,
  "status": "blocked"
}
```

### 4. GET /v1/admin/users/{id}/progress
**Получение прогресса пользователя по модулям**

**Пример:**
```bash
curl -X GET "http://localhost:4000/v1/admin/users/1/progress" \
  -H "Authorization: Bearer admin_token"
```

**Ответ:**
```json
{
  "data": {
    "user_id": 1,
    "passed_modules": [
      {
        "module_id": 1,
        "module_name": "Базовые приветствия",
        "completed_at": "2024-01-01T15:30:00Z",
        "best_time": "00:03:45",
        "attempts": 2,
        "mistakes": 3
      }
    ],
    "total_modules": 10,
    "completion_percentage": 10.0
  }
}
```

## Коды ошибок

- **400 Bad Request** - Неверные параметры запроса
- **401 Unauthorized** - Отсутствует или неверный токен
- **403 Forbidden** - Недостаточно прав (не администратор)
- **404 Not Found** - Пользователь не найден
- **500 Internal Server Error** - Внутренняя ошибка сервера

## Использование в JavaScript/TypeScript

```typescript
const API_BASE = 'http://localhost:4000/v1';
const adminToken = 'your_admin_token';

// Получение списка пользователей
const getUsers = async (filters = {}) => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value) params.append(key, value.toString());
  });

  const response = await fetch(`${API_BASE}/admin/users?${params}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    }
  });

  return response.json();
};

// Получение детальной информации
const getUserDetails = async (userId) => {
  const response = await fetch(`${API_BASE}/admin/users/${userId}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    }
  });

  return response.json();
};

// Изменение статуса пользователя
const updateUserStatus = async (userId, status, reason) => {
  const response = await fetch(`${API_BASE}/admin/users/${userId}/status`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ status, reason })
  });

  return response.json();
};

// Получение прогресса пользователя
const getUserProgress = async (userId) => {
  const response = await fetch(`${API_BASE}/admin/users/${userId}/progress`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    }
  });

  return response.json();
};
```

### 5. POST /v1/admin/create-admin
**Создание нового администратора**

**Описание**: Создает нового пользователя с правами администратора. Доступно только существующим администраторам.

**Параметры запроса:**
```json
{
  "name": "string (required, max 500 chars)",
  "surname": "string (required, max 500 chars)",
  "email": "string (required, valid email)",
  "password": "string (required, min 8 chars)",
  "imageUrl": "string (optional)"
}
```

**Пример запроса:**
```bash
curl -X POST http://localhost:8080/v1/admin/create-admin \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Админ",
    "surname": "Системы",
    "email": "<EMAIL>",
    "password": "securepassword123",
    "imageUrl": ""
  }'
```

**Успешный ответ (201):**
```json
{
  "admin": {
    "user": {
      "id": 5,
      "name": "Админ",
      "surname": "Системы",
      "email": "<EMAIL>",
      "role": "admin",
      "image_url": "",
      "activated": true,
      "created_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

### 6. PUT /v1/admin/users/:id/role
**Изменение роли пользователя**

**Описание**: Изменяет роль указанного пользователя. Администратор не может изменить свою собственную роль.

**Параметры URL:**
- `id` - ID пользователя

**Параметры запроса:**
```json
{
  "role": "string (required, 'admin' or 'user')",
  "reason": "string (optional, причина изменения)"
}
```

**Пример запроса:**
```bash
curl -X PUT http://localhost:8080/v1/admin/users/3/role \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "admin",
    "reason": "Назначение нового администратора"
  }'
```

**Успешный ответ (200):**
```json
{
  "user": {
    "id": 3,
    "name": "Айдар",
    "surname": "Нурланов",
    "email": "<EMAIL>",
    "role": "admin",
    "image_url": "",
    "activated": true,
    "created_at": "2024-01-10T15:20:00Z"
  }
}
```

**Возможные ошибки:**
- `400` - Недопустимая роль или попытка изменить собственную роль
- `404` - Пользователь не найден

## Тестирование

Используйте файл `test-admin-endpoints.html` для тестирования:

1. Откройте файл в браузере
2. Введите токен администратора
3. Протестируйте каждый эндпоинт

## Безопасность

1. **Проверка прав администратора** - только пользователи с ролью `admin`
2. **JWT аутентификация** - все запросы требуют валидный токен
3. **Валидация параметров** - проверка всех входных данных
4. **Логирование** - все изменения статуса и ролей пользователей логируются
5. **Защита от самоизменения** - администратор не может изменить свою собственную роль
6. **Валидация ролей** - проверка допустимых значений ролей ('admin', 'user')

## Расширение функциональности

Для добавления новых админских функций:

1. Добавьте методы в `UserModel` (internal/data/user.go)
2. Создайте обработчики в `cmd/api/admin.go`
3. Добавьте маршруты в `cmd/api/routes.go`
4. Обновите документацию

## Примечания

- Текущая проверка прав администратора основана на email
- Рекомендуется добавить поле `role` в структуру User для более гибкого управления правами
- Все времена возвращаются в формате RFC3339
- Пагинация работает с лимитом до 100 записей на страницу
