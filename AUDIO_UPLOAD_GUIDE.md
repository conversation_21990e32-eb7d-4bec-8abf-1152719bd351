# Руководство по загрузке аудио файлов

## Обзор

Теперь в API добавлена возможность загружать аудио файлы напрямую при создании слов и предложений, вместо передачи URL.

## Новые эндпоинты

### 1. Создание слова с аудио файлом
**POST** `/v1/word/with-audio`

**Content-Type:** `multipart/form-data`

**Параметры:**
- `kaz_plaintext` (string, обязательно) - Текст на казахском языке
- `rus_plaintext` (string, обязательно) - Текст на русском языке  
- `audio` (file, необязательно) - Аудио файл

### 2. Создание предложения с аудио файлом
**POST** `/v1/sentence/with-audio`

**Content-Type:** `multipart/form-data`

**Параметры:**
- `kaz_plaintext` (string, обязательно) - Текст на казахском языке
- `rus_plaintext` (string, обязательно) - Текст на русском языке  
- `audio` (file, необязательно) - Аудио файл

## Поддерживаемые форматы аудио

- MP3 (.mp3)
- WAV (.wav)
- OGG (.ogg)
- M4A (.m4a)

**Максимальный размер файла:** 10MB

## Примеры использования

### cURL

#### Создание слова с аудио:
```bash
curl -X POST http://localhost:4000/v1/word/with-audio \
  -H "Authorization: Bearer your_token" \
  -F "kaz_plaintext=сәлем" \
  -F "rus_plaintext=привет" \
  -F "audio=@path/to/audio.mp3"
```

#### Создание предложения с аудио:
```bash
curl -X POST http://localhost:4000/v1/sentence/with-audio \
  -H "Authorization: Bearer your_token" \
  -F "kaz_plaintext=Сәлем, қалың қалай?" \
  -F "rus_plaintext=Привет, как дела?" \
  -F "audio=@path/to/sentence.mp3"
```

### JavaScript/TypeScript

```typescript
// Создание слова с аудио файлом
const createWordWithAudio = async (wordData: { kaz_plaintext: string; rus_plaintext: string }, audioFile?: File) => {
  const formData = new FormData();
  formData.append('kaz_plaintext', wordData.kaz_plaintext);
  formData.append('rus_plaintext', wordData.rus_plaintext);
  
  if (audioFile) {
    formData.append('audio', audioFile);
  }

  const response = await fetch('/v1/word/with-audio', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData
  });
  
  return response.json();
};

// Использование
const audioFile = document.getElementById('audioInput').files[0];
const result = await createWordWithAudio({
  kaz_plaintext: "сәлем",
  rus_plaintext: "привет"
}, audioFile);
```

## Ответы API

### Успешный ответ (201 Created):
```json
{
  "word": {
    "id": 1,
    "kaz_plaintext": "сәлем",
    "rus_plaintext": "привет",
    "audio_url": "http://minio:9000/klingo-audio/generated-filename.mp3"
  }
}
```

### Ошибки:
- **400 Bad Request** - Неверные параметры или неподдерживаемый формат файла
- **401 Unauthorized** - Отсутствует или неверный токен аутентификации
- **413 Payload Too Large** - Файл превышает максимальный размер (10MB)

## Особенности

1. **Необязательность аудио файла:** Можно создавать слова/предложения без аудио файла
2. **Автоматическая загрузка:** Файл автоматически загружается в MinIO и URL сохраняется в базе данных
3. **Валидация формата:** Проверяется расширение файла перед загрузкой
4. **Обратная совместимость:** Старые методы (`POST /v1/word`, `POST /v1/sentence`) продолжают работать

## Тестирование

Для тестирования используйте файл `test-audio-upload.html` - простую HTML форму для проверки загрузки аудио файлов.

1. Откройте файл в браузере
2. Замените `your_auth_token_here` на реальный токен аутентификации
3. Заполните форму и выберите аудио файл
4. Нажмите "Создать слово" или "Создать предложение"

## Миграция с существующего API

Если вы используете старые методы с `audio_url`, вы можете:

1. **Продолжать использовать старые методы** - они остаются доступными
2. **Перейти на новые методы** для более удобной загрузки файлов
3. **Использовать комбинированный подход** - новые методы для новых записей, старые для обновления существующих

## Получение аудио файлов

### Новые эндпоинты для получения файлов

#### 1. Получение аудио по URL
**GET** `/v1/files/audio?url={full_minio_url}`

```bash
curl -X GET "http://localhost:4000/v1/files/audio?url=http://minio:9000/klingo-audio/334c81d6-e27d-4bc3-bdfe-d58580def252.mp3"
```

#### 2. Получение файла по bucket и имени
**GET** `/v1/files/get?bucket={bucket_name}&filename={filename}`

```bash
curl -X GET "http://localhost:4000/v1/files/get?bucket=klingo-audio&filename=334c81d6-e27d-4bc3-bdfe-d58580def252.mp3"
```

### Использование в HTML

```html
<!-- Прямое воспроизведение аудио -->
<audio controls>
  <source src="http://localhost:4000/v1/files/audio?url=http://minio:9000/klingo-audio/334c81d6-e27d-4bc3-bdfe-d58580def252.mp3" type="audio/mpeg">
  Ваш браузер не поддерживает аудио элемент.
</audio>

<!-- Или через bucket/filename -->
<audio controls>
  <source src="http://localhost:4000/v1/files/get?bucket=klingo-audio&filename=334c81d6-e27d-4bc3-bdfe-d58580def252.mp3" type="audio/mpeg">
  Ваш браузер не поддерживает аудио элемент.
</audio>
```

### Особенности получения файлов

- **Кэширование**: Файлы кэшируются на 1 час (`Cache-Control: public, max-age=3600`)
- **Поддержка Range**: Поддерживается частичная загрузка (`Accept-Ranges: bytes`)
- **Автоматический Content-Type**: Определяется по расширению файла
- **Без аутентификации**: Файлы доступны публично

## Рекомендации

- Используйте новые методы для создания контента с аудио файлами
- Сжимайте аудио файлы перед загрузкой для экономии места и времени
- Проверяйте размер файла на клиентской стороне перед отправкой
- Обрабатывайте ошибки загрузки и показывайте понятные сообщения пользователю
- Используйте эндпоинты получения файлов для воспроизведения аудио в приложении
- Предпочитайте `/v1/files/get` для прямого доступа к файлам по имени
