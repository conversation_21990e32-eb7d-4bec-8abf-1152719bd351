package main

import (
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/olzzhas/kazakh-lingo/internal/data"
	"github.com/olzzhas/kazakh-lingo/internal/validator"
)

func (app *application) loginUserHandler(w http.ResponseWriter, r *http.Request) {
	var input struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}

	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	user, err := app.models.Users.GetByEmail(input.Email)
	if err != nil {
		switch {
		case errors.Is(err, data.ErrRecordNotFound):
			logData := map[string]any{
				"email":   input.Email,
				"success": false,
				"error":   "user not found",
				"time":    time.Now().Format(time.RFC3339),
			}
			app.logger.PrintError(errors.New("user login failed"), logData, "auth")

			app.invalidCredentialsResponse(w, r)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	isPassEquals, err := user.Password.Matches(input.Password)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	if !isPassEquals {
		logData := map[string]any{
			"email":   input.Email,
			"success": false,
			"error":   "incorrect password",
			"time":    time.Now().Format(time.RFC3339),
		}
		app.logger.PrintError(errors.New("user login failed"), logData, "auth")

		app.invalidCredentialsResponse(w, r)
		return
	}

	tokens, err := app.models.AuthorizationTokens.New(int64(user.ID))
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	cookie := http.Cookie{
		Name:     "refreshToken",
		Value:    tokens.RefreshToken,
		Expires:  time.Now().Add(30 * 24 * time.Hour),
		HttpOnly: true,
	}
	http.SetCookie(w, &cookie)

	err = app.writeJSON(w, http.StatusOK, envelope{"response": map[string]any{
		"tokens": tokens,
		"user":   user,
	}}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	logData := map[string]any{
		"email":   input.Email,
		"success": true,
		"time":    time.Now().Format(time.RFC3339),
	}

	app.logger.PrintInfo("User login successful", logData, "auth")
}

func (app *application) registerUserHandler(w http.ResponseWriter, r *http.Request) {
	var input struct {
		Name     string `json:"name"`
		Surname  string `json:"surname"`
		Email    string `json:"email"`
		Password string `json:"password"`
		ImageUrl string `json:"imageUrl"`
	}

	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	user := &data.User{
		Name:      input.Name,
		Surname:   input.Surname,
		Email:     input.Email,
		Role:      data.GetDefaultRole(), // Устанавливаем роль по умолчанию
		ImageUrl:  input.ImageUrl,
		Activated: true,
	}

	err = user.Password.Set(input.Password)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	v := validator.New()

	if data.ValidateUser(v, user); !v.Valid() {
		app.failedValidationResponse(w, r, v.Errors)
		return
	}

	err = app.models.Users.Insert(user)
	if err != nil {
		switch {
		case errors.Is(err, data.ErrDuplicateEmail):
			v.AddError("email", "a user with this email address already exists")
			app.failedValidationResponse(w, r, v.Errors)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	tokens, err := app.models.AuthorizationTokens.New(int64(user.ID))
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	//app.background(func() {
	//	_data := map[string]any{
	//		"activationToken": token.Plaintext,
	//		"userID":          user.ID,
	//	}
	//
	//	err = app.mailer.Send(user.Email, "user_welcome.tmpl", _data)
	//	if err != nil {
	//		app.logger.PrintError(err, nil)
	//	}
	//})

	response := map[string]interface{}{
		"tokens": tokens,
		"user":   user,
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"response": response}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

func (app *application) refreshHandler(w http.ResponseWriter, r *http.Request) {
	cookie, err := r.Cookie("refreshToken")
	if err != nil {
		fmt.Println("Error while getting refresh cookie:", err)
		return
	}
	refreshToken := cookie.Value

	refreshSecret := os.Getenv("REFRESH_SECRET")

	claims, err := app.validateToken(refreshToken, refreshSecret)
	if err != nil {
		app.invalidCredentialsResponse(w, r)
		return
	}

	id := claims["user_id"].(float64)

	v := validator.New()

	if data.ValidateToken(v, refreshToken); !v.Valid() {
		app.failedValidationResponse(w, r, v.Errors)
		return
	}

	refreshToken, err = app.models.AuthorizationTokens.Get(int64(id))
	if err != nil {
		switch {
		case errors.Is(err, sql.ErrNoRows):
			v.AddError("token", "Invalid token")
			app.failedValidationResponse(w, r, v.Errors)
		default:
			app.serverErrorResponse(w, r, err)
		}
		return
	}

	if claims == nil || refreshToken == "" {
		app.invalidCredentialsResponse(w, r)
		return
	}

	tokens, err := app.models.AuthorizationTokens.New(int64(id))
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	newCookie := http.Cookie{
		Name:     "refreshToken",
		Value:    tokens.RefreshToken,
		Expires:  time.Now().Add(30 * 24 * time.Hour),
		HttpOnly: true,
	}
	http.SetCookie(w, &newCookie)

	err = app.writeJSON(w, http.StatusOK, envelope{"tokens": tokens}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}

}

func (app *application) validateToken(tokenString, secret string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("incorrect method for token signing")
		}
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token format")
	}

	return claims, nil
}
