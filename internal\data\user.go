package data

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/olzzhas/kazakh-lingo/internal/validator"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/crypto/bcrypt"
)

var (
	ErrDuplicateEmail = errors.New("duplicate email")
)

// Константы для ролей пользователей
const (
	RoleAdmin = "admin"
	RoleUser  = "user"
)

var AnonymousUser = &User{}

func (u *User) IsAnonymous() bool {
	return u == AnonymousUser
}

// IsAdmin проверяет, является ли пользователь администратором
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// IsUser проверяет, является ли пользователь обычным пользователем
func (u *User) IsUser() bool {
	return u.Role == RoleUser
}

// HasRole проверяет, имеет ли пользователь указанную роль
func (u *User) HasRole(role string) bool {
	return u.Role == role
}

type User struct {
	ID        int       `json:"id"`
	Name      string    `json:"name"`
	Surname   string    `json:"surname"`
	Email     string    `json:"email"`
	Password  password  `json:"-"`
	ImageUrl  string    `json:"image_url"`
	Role      string    `json:"role"`
	Activated bool      `json:"activated"`
	CreatedAt time.Time `json:"created_at"`
}

type password struct {
	plaintext *string
	Hash      []byte
}

type UserModel struct {
	DB    *sql.DB
	Redis *redis.Client
	Mongo *mongo.Client
}

func (p *password) Set(plaintextPassword string) error {
	log.Println("Setting password:", plaintextPassword)
	hash, err := bcrypt.GenerateFromPassword([]byte(plaintextPassword), 12)
	if err != nil {
		return err
	}

	p.plaintext = &plaintextPassword
	p.Hash = hash

	log.Println("Generated hash:", string(hash))

	return nil
}

func (p *password) Matches(plaintextPassword string) (bool, error) {
	if len(p.Hash) == 0 {
		return false, errors.New("password hash is empty or invalid")
	}

	err := bcrypt.CompareHashAndPassword(p.Hash, []byte(plaintextPassword))
	if err != nil {
		switch {
		case errors.Is(err, bcrypt.ErrMismatchedHashAndPassword):
			return false, nil
		default:
			return false, err
		}
	}

	return true, nil
}

func (p *password) UnmarshalJSON(data []byte) error {
	var aux struct {
		Hash string `json:"password_hash"`
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	p.Hash = []byte(aux.Hash)
	return nil
}

func (p *password) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		Hash string `json:"password_hash"`
	}{
		Hash: string(p.Hash),
	})
}

func ValidateEmail(v *validator.Validator, email string) {
	v.Check(email != "", "email", "must be provided")
	v.Check(validator.Matches(email, validator.EmailRX), "email", "must be a valid email address")
}

func ValidatePasswordPlaintext(v *validator.Validator, password string) {
	v.Check(password != "", "password", "must be provided")
	v.Check(len(password) >= 8, "password", "must be at least 8 bytes long")
	v.Check(len(password) <= 72, "password", "must not be more than 72 bytes long")
}

func ValidateUser(v *validator.Validator, user *User) {
	v.Check(user.Name != "", "name", "must be provided")
	v.Check(len(user.Name) <= 500, "name", "must not be more than 500 bytes long")

	v.Check(user.Surname != "", "surname", "must be provided")
	v.Check(len(user.Surname) <= 500, "surname", "must not be more than 500 bytes long")

	// Валидация роли пользователя
	v.Check(user.Role != "", "role", "must be provided")
	v.Check(user.Role == RoleAdmin || user.Role == RoleUser, "role", "must be either 'admin' or 'user'")

	ValidateEmail(v, user.Email)

	if user.Password.plaintext != nil {
		ValidatePasswordPlaintext(v, *user.Password.plaintext)
	}

	if user.Password.Hash == nil {
		panic("missing password hash for user")
	}
}

func (m *UserModel) GetByEmail(email string) (*User, error) {
	query := `
		SELECT id, created_at, name, surname, email, password_hash, role, activated, image_url
		FROM users
		WHERE email = $1
	`

	var user User

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, email).Scan(
		&user.ID,
		&user.CreatedAt,
		&user.Name,
		&user.Surname,
		&user.Email,
		&user.Password.Hash,
		&user.Role,
		&user.Activated,
		&user.ImageUrl,
	)

	if err != nil {
		switch {
		case errors.Is(err, sql.ErrNoRows):
			return nil, ErrRecordNotFound
		default:
			return nil, err
		}
	}

	return &user, nil
}

func (m *UserModel) Get(id int) (*User, error) {
	query := `
		SELECT id, created_at, name, surname, email, password_hash, role, activated, image_url
		FROM users
		WHERE id = $1
	`

	var user User

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, id).Scan(
		&user.ID,
		&user.CreatedAt,
		&user.Name,
		&user.Surname,
		&user.Email,
		&user.Password.Hash,
		&user.Role,
		&user.Activated,
		&user.ImageUrl,
	)

	if err != nil {
		switch {
		case errors.Is(err, sql.ErrNoRows):
			return nil, ErrRecordNotFound
		default:
			return nil, err
		}
	}

	return &user, nil
}

func (m *UserModel) Insert(user *User) error {
	query := `
		INSERT INTO users (name, surname, email, password_hash, role, activated, image_url)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, created_at
	`

	args := []any{user.Name, user.Surname, user.Email, user.Password.Hash, user.Role, user.Activated, user.ImageUrl}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query, args...).Scan(&user.ID, &user.CreatedAt)

	if err != nil {
		switch {
		case err.Error() == `pq: duplicate key value violates unique constraint "users_email_key"`:
			return ErrDuplicateEmail
		default:
			return err
		}
	}

	return nil
}

// UserFilters структура для фильтрации пользователей
type UserFilters struct {
	Page     int
	Limit    int
	Search   string
	Status   string // "active", "blocked"
	DateFrom string
	DateTo   string
}

// UserListResponse структура ответа со списком пользователей
type UserListResponse struct {
	Users      []UserWithStats `json:"users"`
	Total      int             `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

// UserWithStats пользователь с дополнительной статистикой
type UserWithStats struct {
	User
	LastActivity     *time.Time `json:"last_activity"`
	CompletedModules int        `json:"completed_modules"`
	TotalTimeSpent   string     `json:"total_time_spent"`
	CurrentStreak    int        `json:"current_streak"`
}

// GetAll получение списка пользователей с фильтрацией
func (m *UserModel) GetAll(filters UserFilters) (*UserListResponse, error) {
	// Устанавливаем значения по умолчанию
	if filters.Page < 1 {
		filters.Page = 1
	}
	if filters.Limit < 1 {
		filters.Limit = 20
	}

	// Базовый запрос
	baseQuery := `
		SELECT
			u.id, u.created_at, u.name, u.surname, u.email, u.role, u.activated, u.image_url,
			MAX(p.created_at) as last_activity,
			COUNT(DISTINCT p.module_id) as completed_modules,
			COALESCE(SUM(EXTRACT(EPOCH FROM p.time::interval)), 0) as total_time_seconds
		FROM users u
		LEFT JOIN progress p ON u.id = p.user_id
		WHERE 1=1
	`

	countQuery := `
		SELECT COUNT(DISTINCT u.id)
		FROM users u
		LEFT JOIN progress p ON u.id = p.user_id
		WHERE 1=1
	`

	args := []interface{}{}
	argIndex := 1

	// Добавляем фильтры
	if filters.Search != "" {
		searchFilter := ` AND (u.name ILIKE $` + fmt.Sprintf("%d", argIndex) + ` OR u.surname ILIKE $` + fmt.Sprintf("%d", argIndex) + ` OR u.email ILIKE $` + fmt.Sprintf("%d", argIndex) + `)`
		baseQuery += searchFilter
		countQuery += searchFilter
		args = append(args, "%"+filters.Search+"%")
		argIndex++
	}

	if filters.Status == "active" {
		statusFilter := ` AND u.activated = $` + fmt.Sprintf("%d", argIndex)
		baseQuery += statusFilter
		countQuery += statusFilter
		args = append(args, true)
		argIndex++
	} else if filters.Status == "blocked" {
		statusFilter := ` AND u.activated = $` + fmt.Sprintf("%d", argIndex)
		baseQuery += statusFilter
		countQuery += statusFilter
		args = append(args, false)
		argIndex++
	}

	if filters.DateFrom != "" {
		dateFilter := ` AND u.created_at >= $` + fmt.Sprintf("%d", argIndex)
		baseQuery += dateFilter
		countQuery += dateFilter
		args = append(args, filters.DateFrom)
		argIndex++
	}

	if filters.DateTo != "" {
		dateFilter := ` AND u.created_at <= $` + fmt.Sprintf("%d", argIndex)
		baseQuery += dateFilter
		countQuery += dateFilter
		args = append(args, filters.DateTo)
		argIndex++
	}

	// Добавляем группировку, сортировку и пагинацию
	baseQuery += `
		GROUP BY u.id, u.created_at, u.name, u.surname, u.email, u.activated, u.image_url
		ORDER BY u.created_at DESC
		LIMIT $` + fmt.Sprintf("%d", argIndex) + ` OFFSET $` + fmt.Sprintf("%d", argIndex+1)

	offset := (filters.Page - 1) * filters.Limit
	args = append(args, filters.Limit, offset)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Получаем общее количество
	var total int
	err := m.DB.QueryRowContext(ctx, countQuery, args[:len(args)-2]...).Scan(&total)
	if err != nil {
		return nil, err
	}

	// Получаем пользователей
	rows, err := m.DB.QueryContext(ctx, baseQuery, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []UserWithStats
	for rows.Next() {
		var user UserWithStats
		var totalTimeSeconds sql.NullFloat64
		var lastActivity sql.NullTime

		err := rows.Scan(
			&user.ID,
			&user.CreatedAt,
			&user.Name,
			&user.Surname,
			&user.Email,
			&user.Role,
			&user.Activated,
			&user.ImageUrl,
			&lastActivity,
			&user.CompletedModules,
			&totalTimeSeconds,
		)
		if err != nil {
			return nil, err
		}

		if lastActivity.Valid {
			user.LastActivity = &lastActivity.Time
		}

		// Конвертируем секунды в формат HH:MM:SS
		if totalTimeSeconds.Valid {
			totalSeconds := int(totalTimeSeconds.Float64)
			hours := totalSeconds / 3600
			minutes := (totalSeconds % 3600) / 60
			seconds := totalSeconds % 60
			user.TotalTimeSpent = fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds)
		} else {
			user.TotalTimeSpent = "00:00:00"
		}

		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	totalPages := (total + filters.Limit - 1) / filters.Limit

	return &UserListResponse{
		Users:      users,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

// UserDetails детальная информация о пользователе
type UserDetails struct {
	User         User                `json:"user"`
	Progress     UserProgressSummary `json:"progress"`
	Achievements []UserAchievement   `json:"achievements"`
}

// UserProgressSummary сводка по прогрессу пользователя
type UserProgressSummary struct {
	CompletedModules     int     `json:"completed_modules"`
	TotalModules         int     `json:"total_modules"`
	CompletionPercentage float64 `json:"completion_percentage"`
	TotalTimeSpent       string  `json:"total_time_spent"`
	CurrentStreak        int     `json:"current_streak"`
}

// GetUserDetails получение детальной информации о пользователе
func (m *UserModel) GetUserDetails(userID int) (*UserDetails, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Получаем основную информацию о пользователе
	user, err := m.Get(userID)
	if err != nil {
		return nil, err
	}

	// Получаем прогресс пользователя
	progressQuery := `
		SELECT
			COUNT(DISTINCT p.module_id) as completed_modules,
			(SELECT COUNT(*) FROM modules) as total_modules,
			COALESCE(SUM(EXTRACT(EPOCH FROM p.time::interval)), 0) as total_time_seconds
		FROM progress p
		WHERE p.user_id = $1
	`

	var completedModules, totalModules int
	var totalTimeSeconds float64

	err = m.DB.QueryRowContext(ctx, progressQuery, userID).Scan(
		&completedModules,
		&totalModules,
		&totalTimeSeconds,
	)
	if err != nil {
		return nil, err
	}

	// Конвертируем секунды в формат HH:MM:SS
	totalSeconds := int(totalTimeSeconds)
	hours := totalSeconds / 3600
	minutes := (totalSeconds % 3600) / 60
	seconds := totalSeconds % 60
	totalTimeSpent := fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds)

	// Вычисляем процент завершения
	var completionPercentage float64
	if totalModules > 0 {
		completionPercentage = float64(completedModules) / float64(totalModules) * 100
	}

	// Получаем текущую серию
	streakData, err := m.getStreakForUser(int64(userID))
	currentStreak := 0
	if err == nil {
		if streak, ok := streakData["current_streak"]; ok {
			currentStreak = streak
		}
	}

	// Получаем достижения пользователя
	achievementsQuery := `
		SELECT
			ua.achievement_id,
			ua.user_id,
			ua.progress,
			ua.achieved,
			a.name,
			a.description,
			a.type,
			a.target
		FROM user_achievements ua
		JOIN achievements a ON ua.achievement_id = a.id
		WHERE ua.user_id = $1 AND ua.achieved = true
		ORDER BY ua.updated_at DESC
	`

	rows, err := m.DB.QueryContext(ctx, achievementsQuery, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var achievements []UserAchievement
	for rows.Next() {
		var achievement UserAchievement
		var name, description, achievementType string
		var target int

		err := rows.Scan(
			&achievement.AchievementID,
			&achievement.UserID,
			&achievement.Progress,
			&achievement.Achieved,
			&name,
			&description,
			&achievementType,
			&target,
		)
		if err != nil {
			return nil, err
		}

		achievements = append(achievements, achievement)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return &UserDetails{
		User: *user,
		Progress: UserProgressSummary{
			CompletedModules:     completedModules,
			TotalModules:         totalModules,
			CompletionPercentage: completionPercentage,
			TotalTimeSpent:       totalTimeSpent,
			CurrentStreak:        currentStreak,
		},
		Achievements: achievements,
	}, nil
}

// UpdateUserStatus обновление статуса пользователя
func (m *UserModel) UpdateUserStatus(userID int, activated bool, reason string) error {
	query := `
		UPDATE users
		SET activated = $2
		WHERE id = $1
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	result, err := m.DB.ExecContext(ctx, query, userID, activated)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	// Логируем изменение статуса (можно расширить для сохранения в отдельную таблицу)
	log.Printf("User %d status changed to activated=%t, reason: %s", userID, activated, reason)

	return nil
}

// UpdateUserRole обновление роли пользователя
func (m *UserModel) UpdateUserRole(userID int, role string, reason string) error {
	// Проверяем, что роль допустима
	if !IsValidRole(role) {
		return errors.New("invalid role")
	}

	query := `
		UPDATE users
		SET role = $2
		WHERE id = $1
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	result, err := m.DB.ExecContext(ctx, query, userID, role)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrRecordNotFound
	}

	// Логируем изменение роли
	log.Printf("User %d role changed to %s, reason: %s", userID, role, reason)

	return nil
}

// getStreakForUser вспомогательный метод для получения серии пользователя
func (m *UserModel) getStreakForUser(userID int64) (map[string]int, error) {
	query := `
		SELECT created_at
		FROM progress
		WHERE user_id = $1
		ORDER BY created_at ASC
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	rows, err := m.DB.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var dates []time.Time
	for rows.Next() {
		var createdAt time.Time
		err := rows.Scan(&createdAt)
		if err != nil {
			return nil, err
		}
		dates = append(dates, createdAt)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	currentStreak, maxStreak := calculateStreaks(dates)

	return map[string]int{
		"current_streak": currentStreak,
		"max_streak":     maxStreak,
	}, nil
}

// calculateStreaks вспомогательная функция для расчета серий
func calculateStreaks(dates []time.Time) (int, int) {
	if len(dates) == 0 {
		return 0, 0
	}

	currentStreak := 0
	maxStreak := 0
	tempStreak := 1

	now := time.Now().Truncate(24 * time.Hour)

	for i := 1; i < len(dates); i++ {
		prevDate := dates[i-1].Truncate(24 * time.Hour)
		currDate := dates[i].Truncate(24 * time.Hour)

		if currDate.Sub(prevDate).Hours() <= 24 {
			tempStreak++
		} else {
			if tempStreak > maxStreak {
				maxStreak = tempStreak
			}
			tempStreak = 1
		}
	}

	if tempStreak > maxStreak {
		maxStreak = tempStreak
	}

	lastActivity := dates[len(dates)-1].Truncate(24 * time.Hour)
	if now.Sub(lastActivity).Hours() <= 24 {
		currentStreak = tempStreak
	}

	return currentStreak, maxStreak
}
