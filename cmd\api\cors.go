package main

import (
	"net/http"

	"github.com/rs/cors"
)

func CorsSettings() *cors.Cors {
	c := cors.New(cors.Options{
		AllowedMethods: []string{
			http.MethodPost, http.MethodGet, http.MethodDelete, http.MethodPatch, http.MethodOptions, http.MethodPut, http.MethodTrace,
		},
		AllowedOrigins: []string{
			"http://localhost:3000",
			"http://localhost:5173",
			"http://localhost:5174",
			"http://localhost:5175",
			"http://localhost:5176",
			"http://localhost:5177",
		},
		AllowCredentials: true,
		AllowedHeaders: []string{
			"Content-Type",
			"Authorization",
		},
		// OptionsPassthrough: true,
		ExposedHeaders: []string{
			"Content-Type",
		},
		Debug: false,
	})
	return c
}
