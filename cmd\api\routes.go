package main

import (
	"expvar"
	"net/http"

	"github.com/julienschmidt/httprouter"
)

func (app *application) routes() http.Handler {
	router := httprouter.New()

	router.NotFound = http.HandlerFunc(app.notFoundResponse)
	router.MethodNotAllowed = http.HandlerFunc(app.methodNotAllowedResponse)
	//Health Check
	router.HandlerFunc(http.MethodGet, "/v1/healthcheck", app.healthcheckHandler)
	router.Handler(http.MethodGet, "/debug/vars", expvar.Handler())

	router.HandlerFunc(http.MethodPost, "/v1/auth/login", app.loginUserHandler)
	router.HandlerFunc(http.MethodPost, "/v1/auth/register", app.registerUserHandler)

	router.HandlerFunc(http.MethodPost, "/v1/questions", app.createQuestionHandler)
	router.HandlerFunc(http.MethodGet, "/v1/questions/all", app.listQuestionsHandler)
	router.HandlerFunc(http.MethodGet, "/v1/questions", app.showQuestionHandler)

	router.HandlerFunc(http.MethodPost, "/v1/word", app.createWordHandler)
	router.HandlerFunc(http.MethodPost, "/v1/word/with-audio", app.createWordWithAudioHandler)
	router.HandlerFunc(http.MethodGet, "/v1/word", app.listWordsHandler)

	router.HandlerFunc(http.MethodPost, "/v1/sentence", app.createSentenceHandler)
	router.HandlerFunc(http.MethodPost, "/v1/sentence/with-audio", app.createSentenceWithAudioHandler)
	router.HandlerFunc(http.MethodGet, "/v1/sentence", app.showSentenceHandler)
	router.HandlerFunc(http.MethodGet, "/v1/sentence/all", app.listSentencesHandler)

	router.HandlerFunc(http.MethodGet, "/v1/theory", app.showTheoryHandler)
	router.HandlerFunc(http.MethodGet, "/v1/theory/all", app.listTheoriesHandler)
	router.HandlerFunc(http.MethodPost, "/v1/theory", app.createTheoryHandler)
	router.HandlerFunc(http.MethodPut, "/v1/theory", app.updateTheoryHandler)
	router.HandlerFunc(http.MethodDelete, "/v1/theory", app.deleteTheoryHandler)

	router.HandlerFunc(http.MethodPost, "/v1/module", app.createModuleHandler)
	router.HandlerFunc(http.MethodGet, "/v1/module/all", app.listModulesHandler)
	router.HandlerFunc(http.MethodGet, "/v1/module", app.getFullModuleHandler)
	router.HandlerFunc(http.MethodPut, "/v1/module", app.updateModuleHandler)
	router.HandlerFunc(http.MethodDelete, "/v1/module", app.deleteModuleHandler)
	router.HandlerFunc(http.MethodGet, "/v1/module-user-progress/:id", app.getUserPassedModulesHandler)

	router.HandlerFunc(http.MethodPost, "/v1/progress/save", app.saveProgressHandler)
	router.HandlerFunc(http.MethodGet, "/v1/progress/streak/:id", app.checkStreakHandler)

	router.HandlerFunc(http.MethodPost, "/v1/achievements", app.createAchievementHandler)
	router.HandlerFunc(http.MethodGet, "/v1/achievements-all", app.listAchievementsHandler)
	router.HandlerFunc(http.MethodGet, "/v1/achievements/:id", app.listUserAchievementsHandler)
	router.HandlerFunc(http.MethodPatch, "/v1/achievements", app.updateAchievementProgressHandlerLegacy)

	// Маршруты для работы с файлами (аудио и изображения)
	router.HandlerFunc(http.MethodPost, "/v1/files/upload/audio", app.uploadAudioHandler)
	router.HandlerFunc(http.MethodPost, "/v1/files/upload/image", app.uploadImageHandler)
	router.HandlerFunc(http.MethodPost, "/v1/files/upload/multiple", app.uploadMultipleFilesHandler)
	router.HandlerFunc(http.MethodDelete, "/v1/files/delete", app.deleteFileHandler)
	router.HandlerFunc(http.MethodGet, "/v1/files/list", app.listFilesHandler)
	router.HandlerFunc(http.MethodGet, "/v1/files/audio", app.getAudioHandler)
	router.HandlerFunc(http.MethodGet, "/v1/files/get", app.getFileHandler)

	// Админские маршруты
	router.HandlerFunc(http.MethodGet, "/v1/admin/users", app.adminMiddleware(app.getUsersHandler))
	router.HandlerFunc(http.MethodGet, "/v1/admin/users/:id", app.adminMiddleware(app.getUserDetailsHandler))
	router.HandlerFunc(http.MethodPut, "/v1/admin/users/:id/status", app.adminMiddleware(app.updateUserStatusHandler))
	router.HandlerFunc(http.MethodGet, "/v1/admin/users/:id/progress", app.adminMiddleware(app.getUserProgressHandler))

	// Управление ролями и создание админов
	router.HandlerFunc(http.MethodPost, "/v1/admin/create-admin", app.adminMiddleware(app.createAdminHandler))
	router.HandlerFunc(http.MethodPut, "/v1/admin/users/:id/role", app.adminMiddleware(app.updateUserRoleHandler))

	return app.metrics(app.recoverPanic(app.rateLimit(app.authenticate(router))))

}
